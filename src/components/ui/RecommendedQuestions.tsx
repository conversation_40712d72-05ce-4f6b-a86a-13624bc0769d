'use client'

import React from 'react'
import { MessageCircle, Lightbulb, Search, BarChart3 } from 'lucide-react'
import { RecommendedQuestion } from '@/lib/store'
import { cn } from '@/lib/utils'

interface RecommendedQuestionsProps {
  questions: RecommendedQuestion[]
  onQuestionClick: (question: string) => void
  className?: string
}

const categoryIcons = {
  understanding: MessageCircle,
  application: Lightbulb,
  exploration: Search,
  analysis: BarChart3
}

const categoryColors = {
  understanding: 'from-blue-500 to-blue-600',
  application: 'from-green-500 to-green-600',
  exploration: 'from-purple-500 to-purple-600',
  analysis: 'from-orange-500 to-orange-600'
}

const RecommendedQuestions: React.FC<RecommendedQuestionsProps> = ({
  questions,
  onQuestionClick,
  className
}) => {
  if (!questions || questions.length === 0) {
    return null
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* 标题 */}
      <div className="flex items-center space-x-2 px-1">
        <MessageCircle className="w-4 h-4 text-gray-600" />
        <h4 className="text-sm font-medium text-gray-700">推荐问题</h4>
      </div>

      {/* 问题卡片 */}
      <div className="grid grid-cols-1 gap-2">
        {questions.map((question) => {
          const IconComponent = categoryIcons[question.category as keyof typeof categoryIcons] || MessageCircle
          const colorClass = categoryColors[question.category as keyof typeof categoryColors] || categoryColors.understanding

          return (
            <button
              key={question.id}
              onClick={() => onQuestionClick(question.question)}
              className={cn(
                'group relative overflow-hidden rounded-2xl p-4 text-left transition-all duration-200',
                'bg-white border border-gray-200/60 shadow-sm',
                'hover:shadow-md hover:border-gray-300/60 hover:scale-[1.02]',
                'active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-blue-500/20'
              )}
            >
              {/* 背景渐变效果 */}
              <div className={cn(
                'absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-5 transition-opacity duration-200',
                colorClass
              )} />

              {/* 内容 */}
              <div className="relative flex items-start space-x-3">
                {/* 图标 */}
                <div className={cn(
                  'flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center',
                  'bg-gradient-to-r text-white shadow-sm',
                  colorClass
                )}>
                  <IconComponent className="w-4 h-4" />
                </div>

                {/* 问题文本 */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 leading-relaxed group-hover:text-gray-800 transition-colors">
                    {question.question}
                  </p>
                </div>

                {/* 箭头指示器 */}
                <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <div className="w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center">
                    <svg className="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </button>
          )
        })}
      </div>
    </div>
  )
}

export default RecommendedQuestions
