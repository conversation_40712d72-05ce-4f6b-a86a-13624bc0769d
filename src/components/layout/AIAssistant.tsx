'use client'

import React, { useState, useRef, useEffect } from 'react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import ModernLoader from '@/components/ui/ModernLoader'
import RecommendedQuestions from '@/components/ui/RecommendedQuestions'
import { useAppStore, useActiveTab, useActiveChatMessages, useActiveStreamingNote, useActiveRecommendedQuestions } from '@/lib/store'
import { Send, Brain, Sparkles, ChevronDown, ChevronUp } from 'lucide-react'

const AIAssistant: React.FC = () => {
  const { addChatMessage, updateChatMessage, setRecommendedQuestions } = useAppStore()
  const activeTab = useActiveTab()
  const chatMessages = useActiveChatMessages()
  const streamingNote = useActiveStreamingNote()
  const recommendedQuestions = useActiveRecommendedQuestions()
  const [chatInput, setChatInput] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const [structuredNotesHeight, setStructuredNotesHeight] = useState(20) // 默认20%高度
  const [isStructuredNotesExpanded, setIsStructuredNotesExpanded] = useState(true) // 结构化笔记展开状态
  const [isDragging, setIsDragging] = useState(false)
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const dragHandleRef = useRef<HTMLDivElement>(null)

  // 判断是否有聊天记录
  const hasChatMessages = chatMessages.length > 0

  // 生成推荐问题
  const generateRecommendedQuestions = async () => {
    if (!activeTab || !activeTab.originalContent) return

    try {
      const response = await fetch('/api/generate-questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: activeTab.originalContent,
          aiNote: streamingNote || activeTab.aiNoteMarkdown
        }),
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.questions) {
          setRecommendedQuestions(activeTab.id, data.questions)
        }
      }
    } catch (error) {
      console.error('生成推荐问题失败:', error)
    }
  }

  // 处理推荐问题点击
  const handleQuestionClick = (question: string) => {
    setChatInput(question)
  }

  // 自动滚动到聊天底部
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
    }
  }, [chatMessages, isStreaming])

  // 在结构化笔记生成完成后生成推荐问题
  useEffect(() => {
    if (activeTab && !activeTab.aiAnalyzing && activeTab.aiNoteMarkdown && recommendedQuestions.length === 0) {
      // 延迟一下确保结构化笔记完全生成
      const timer = setTimeout(() => {
        generateRecommendedQuestions()
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [activeTab?.aiAnalyzing, activeTab?.aiNoteMarkdown, recommendedQuestions.length])

  // 拖拽功能实现
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return

      const containerHeight = window.innerHeight - 200 // 减去其他UI元素的高度
      const newHeight = Math.max(10, Math.min(80, (e.clientY / containerHeight) * 100))
      setStructuredNotesHeight(newHeight)
    }

    const handleMouseUp = () => {
      setIsDragging(false)
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging])

  const handleDragStart = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  // 处理流式聊天
  const handleChatSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!chatInput.trim() || !activeTab || isStreaming) return

    const input = chatInput.trim()
    setChatInput('')
    setIsStreaming(true)

    // 如果是第一次聊天，自动收起结构化笔记以节省空间（改为20%高度）
    if (chatMessages.length === 0) {
      setStructuredNotesHeight(20)
    }

    try {
      // 添加用户消息
      addChatMessage(activeTab.id, {
        role: 'user',
        content: input
      })

      // 创建AI回复的临时消息
      const assistantMessageId = addChatMessage(activeTab.id, {
        role: 'assistant',
        content: ''
      })

      // 发送请求到聊天API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: input,
          context: {
            originalContent: activeTab.originalContent || '',
            aiNote: streamingNote || activeTab.aiNoteMarkdown || '',
          }
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      let fullResponse = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') continue

            try {
              const parsed = JSON.parse(data)
              if (parsed.content) {
                fullResponse += parsed.content
                updateChatMessage(activeTab.id, assistantMessageId, fullResponse)
              }
            } catch (e) {
              console.warn('解析JSON失败:', e)
            }
          }
        }
      }

      if (!fullResponse) {
        updateChatMessage(activeTab.id, assistantMessageId, '抱歉，AI暂时无法回复，请重试。')
      }
    } catch (error) {
      console.error('聊天请求失败:', error)
      addChatMessage(activeTab.id, {
        role: 'assistant',
        content: '抱歉，发生了错误，请重试。'
      })
    } finally {
      setIsStreaming(false)
    }
  }

  if (!activeTab) {
    return (
      <div className="h-full flex items-center justify-center text-gray-500 p-6">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center">
            <Brain className="w-8 h-8 text-blue-600" />
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900 mb-2">AI助手</p>
            <p className="text-sm text-gray-600">选择或创建一个标签页开始使用</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col liquid-glass">
      {/* 根据聊天状态决定布局 */}
      {!hasChatMessages ? (
        /* 无聊天时：结构化笔记占满全屏，无卡片边框 */
        <div className="h-full flex flex-col p-6">
          {/* 简洁的标题 */}
          <div className="flex items-center space-x-3 mb-6">
            <Sparkles className="w-5 h-5 text-blue-500" />
            <h2 className="text-lg font-medium text-gray-900">结构化笔记</h2>
            {activeTab && activeTab.aiAnalyzing && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                生成中
              </span>
            )}
          </div>

          {/* 结构化笔记内容区域 - 无边框，自由滚动 */}
          <div className="flex-1 overflow-y-auto">
            {!activeTab ? (
              <div className="h-full flex items-center justify-center text-gray-500">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center">
                    <Brain className="w-8 h-8 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-lg font-semibold text-gray-900 mb-2">AI助手</p>
                    <p className="text-sm text-gray-600">选择或创建一个标签页开始使用</p>
                  </div>
                </div>
              </div>
            ) : activeTab.isLoading ? (
              <div className="h-full flex items-center justify-center">
                <ModernLoader variant="dots" size="lg" text="正在分析内容..." className="text-center" />
              </div>
            ) : (streamingNote || activeTab.aiNoteMarkdown) ? (
              /* 无聊天时：结构化笔记无边框，自由滚动 */
              <SafeMarkdown className="prose prose-lg max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-6 prose-blockquote:py-2 prose-blockquote:rounded-r-lg">
                {streamingNote || activeTab.aiNoteMarkdown || ''}
              </SafeMarkdown>
            ) : activeTab.aiAnalyzing ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <ModernLoader variant="dots" size="lg" text="正在生成结构化笔记..." className="text-center" />
                  <p className="text-sm text-gray-500 mt-4">AI正在为您整理知识要点</p>
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 mx-auto bg-gray-100 rounded-2xl flex items-center justify-center">
                    <Brain className="w-8 h-8 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-gray-500 text-lg font-medium">准备就绪</p>
                    <p className="text-gray-400 text-sm mt-2">开始输入内容，AI将为您生成结构化笔记</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 聊天输入框 - 圆润设计，与有聊天状态保持一致 */}
          <div className="mt-6 pt-4">
            <form onSubmit={handleChatSubmit} className="flex space-x-3 bg-white/95 backdrop-blur-md rounded-full border border-gray-200/50 p-3 shadow-xl">
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  placeholder="对内容有疑问？问我任何问题..."
                  disabled={isStreaming || !activeTab}
                  className="w-full px-4 py-2 border-0 bg-transparent focus:ring-0 focus:outline-none text-sm placeholder-gray-500 disabled:text-gray-400 rounded-full"
                />
              </div>
              <button
                type="submit"
                disabled={!chatInput.trim() || isStreaming || !activeTab}
                className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95"
              >
                <Send size={14} />
              </button>
            </form>
          </div>
        </div>
      ) : (
        /* 有聊天时：新的布局 - 悬浮结构化笔记卡片 + 全高度AI助手 */
        <div className="h-full relative">
          {/* AI助手对话容器 - 占用全部高度，智能滚动从结构化笔记底部开始 */}
          <div className="h-full flex flex-col">
            <div
              ref={chatContainerRef}
              className="flex-1 overflow-y-auto space-y-6 bg-transparent"
              style={{
                paddingTop: `calc(${structuredNotesHeight}% + 2rem)`, // 为结构化笔记卡片留出空间
                paddingLeft: '1rem',
                paddingRight: '1rem',
                paddingBottom: '1rem'
              }}
            >
              {chatMessages.map((message) => {
                if (!message.id || !message.content) return null
                return (
                  <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[85%] px-5 py-4 text-sm shadow-lg backdrop-blur-sm ${
                      message.role === 'user'
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-3xl border border-blue-400/20'
                        : 'bg-white/95 border border-gray-200/60 text-gray-900 rounded-3xl'
                    }`}>
                      {message.role === 'assistant' ? (
                        <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-gray-800 prose-p:text-gray-700 prose-ul:text-gray-700 prose-ol:text-gray-700 prose-code:text-blue-600 prose-code:bg-blue-50">
                          {message.content || '正在思考...'}
                        </SafeMarkdown>
                      ) : (
                        <span>{message.content}</span>
                      )}
                    </div>
                  </div>
                )
              }).filter(Boolean)}

              {isStreaming && (
                <div className="flex justify-start">
                  <div className="bg-white/95 border border-gray-200/60 rounded-3xl px-5 py-4 shadow-lg backdrop-blur-sm">
                    <div className="flex items-center space-x-3">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                      </div>
                      <span className="text-sm text-gray-500">AI正在思考...</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 固定在底部的输入框 - 圆润设计，透明底部容器 */}
            <div className="flex-shrink-0 relative bg-transparent">
              <div className="p-3 mx-4 mb-3">
                <form onSubmit={handleChatSubmit} className="flex space-x-3 bg-white/95 backdrop-blur-md rounded-full border border-gray-200/50 p-3 shadow-xl">
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      value={chatInput}
                      onChange={(e) => setChatInput(e.target.value)}
                      placeholder="输入消息..."
                      disabled={isStreaming}
                      className="w-full px-4 py-2 border-0 bg-transparent focus:ring-0 focus:outline-none text-sm placeholder-gray-500 disabled:text-gray-400 rounded-full"
                    />
                  </div>
                  <button
                    type="submit"
                    disabled={!chatInput.trim() || isStreaming}
                    className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95"
                  >
                    <Send size={14} />
                  </button>
                </form>
              </div>
            </div>
          </div>

          {/* 悬浮的结构化笔记卡片 - 高z-index，可拖拽调整高度，更圆润设计，修复透明度问题 */}
          <div
            className="absolute top-4 left-4 right-4 bg-white backdrop-blur-lg rounded-3xl border border-slate-200/50 shadow-2xl shadow-slate-200/40"
            style={{
              height: `${structuredNotesHeight}%`,
              zIndex: 60, // 提高z-index确保在AI助手内容之上
              minHeight: '120px',
              maxHeight: '80%'
            }}
          >
            {/* 卡片头部 */}
            <div className="px-5 py-4 flex items-center justify-between border-b border-slate-200/50">
              <div className="flex items-center space-x-3">
                <div className="p-1.5 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
                <h3 className="font-semibold text-slate-800 text-sm">结构化笔记</h3>
                {activeTab?.aiAnalyzing && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 border border-blue-200/50">
                    🔄 生成中
                  </span>
                )}
              </div>

              {/* 折叠展开按钮 */}
              <button
                onClick={() => setIsStructuredNotesExpanded(!isStructuredNotesExpanded)}
                className="p-1.5 rounded-lg hover:bg-slate-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
                title={isStructuredNotesExpanded ? "折叠" : "展开"}
              >
                {isStructuredNotesExpanded ? (
                  <ChevronUp className="w-4 h-4 text-slate-600" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-slate-600" />
                )}
              </button>
            </div>

            {/* 卡片内容 - 可滚动，支持折叠展开 */}
            {isStructuredNotesExpanded && (
              <div className="flex-1 overflow-y-auto px-4 py-4" style={{ height: 'calc(100% - 80px)' }}>
                {activeTab?.isLoading ? (
                  <div className="py-8">
                    <ModernLoader variant="dots" size="md" text="正在分析..." className="text-center" />
                  </div>
                ) : (streamingNote || activeTab?.aiNoteMarkdown) ? (
                  <div className="space-y-4">
                    <div className="ai-note-content bg-gradient-to-br from-slate-50/50 to-blue-50/30 rounded-xl p-4">
                      <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:rounded-r-lg">
                        {streamingNote || activeTab?.aiNoteMarkdown || ''}
                      </SafeMarkdown>
                    </div>

                    {/* 推荐问题卡片 */}
                    {recommendedQuestions.length > 0 && (
                      <RecommendedQuestions
                        questions={recommendedQuestions}
                        onQuestionClick={handleQuestionClick}
                        className="mt-4"
                      />
                    )}
                  </div>
                ) : activeTab?.aiAnalyzing ? (
                  <div className="py-8">
                    <ModernLoader variant="dots" size="md" text="正在生成结构化笔记..." className="text-center" />
                  </div>
                ) : (
                  <div className="py-8 text-center">
                    <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-3">
                      <Brain className="w-6 h-6 text-gray-400" />
                    </div>
                    <p className="text-gray-500 text-sm">暂无结构化笔记</p>
                  </div>
                )}
              </div>
            )}

            {/* 拖拽手柄 - 更圆润的设计 */}
            <div
              ref={dragHandleRef}
              onMouseDown={handleDragStart}
              className="absolute bottom-0 left-0 right-0 h-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-b-3xl cursor-ns-resize hover:from-blue-500/40 hover:to-purple-500/40 transition-all duration-200"
              title="拖拽调整高度"
            >
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-1.5 bg-slate-400/80 rounded-full"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AIAssistant
